import { defineStore } from "pinia";
import getOverview from "@/modules/Overview/Overview";
import { calculateErrors } from "@/helpers/uiChart";
import type {
	GetOverviewParams,
	DateRangeType,
	FunnelData,
	StatsData,
	<PERSON>rrorMapping<PERSON>eys,
	CheckinSourceData,
} from "@/types";

interface UpdateDateRangeSingleBrand {
	brand_id: number;
	name: ErrorMappingKeys;
	range: DateRangeType;
}
interface State {
	search_funnel: FunnelData[] | null;
	guest_funnel: FunnelData[] | null;
	checkin: StatsData[] | null;
	reservation: StatsData[] | null;
	scan: StatsData[] | null;
	previousReservation: StatsData[] | null;
	previousScan: StatsData[] | null;
	previousCheckin: StatsData[] | null;
	reservation_errors?: Array<OverviewErrors> | null;
	scan_errors?: Array<OverviewErrors> | null;
	checkin_errors?: Array<OverviewErrors> | null;
	checkinSource: CheckinSourceData[] | null;
}
interface OverviewErrors {
	name: string;
	data: Array<OverviewData>;
}
interface OverviewData {
	x: string;
	y: number;
}

export const useOverviewStore = defineStore("overview", {
	// arrow function recommended for full type inference
	state: (): State => {
		return {
			// all these properties will have their type inferred automatically
			search_funnel: null,
			guest_funnel: null,
			checkin: null,
			reservation: null,
			scan: null,
			previousReservation: null,
			previousScan: null,
			previousCheckin: null,
			reservation_errors: null,
			scan_errors: null,
			checkin_errors: null,
			checkinSource: null,
		};
	},
	actions: {
		async getOverview({
			brand_id,
			range,
			isReceptionModeEnabled = false,
		}: GetOverviewParams) {
			try {
				this.reset();
				const stats = await getOverview({
					brand_id,
					range,
					isReceptionModeEnabled,
				});

				this.search_funnel = stats.search_funnel ?? [];
				this.guest_funnel = stats.guest_funnel ?? [];
				this.checkin = stats.current?.checkin ?? [];
				this.reservation = stats.current?.reservation ?? [];
				this.scan = stats.current?.scan ?? [];
				this.previousCheckin = stats.previous?.checkin ?? [];
				this.previousReservation = stats.previous?.reservation ?? [];
				this.previousScan = stats.previous?.scan ?? [];
				this.reservation_errors = calculateErrors(stats.current?.reservation);
				this.scan_errors = calculateErrors(stats.current?.scan);
				this.checkin_errors = calculateErrors(stats.current?.checkin);
				this.checkinSource = stats.checkinSource ?? [];

				return Promise.resolve();
			} catch (error) {
				return Promise.reject();
			}
		},
		async getParticularChart({
			brand_id,
			name,
			range,
		}: UpdateDateRangeSingleBrand): Promise<void> {
			console.log('🔍 getParticularChart called with:', { brand_id, name, range });

			// reset the store attribute of the corresponding category
			this[name] = null;

			//we separate in the store reservation from reservation errors but the overview search type is the same
			const typeMapping: Record<ErrorMappingKeys, string> = {
				scan_errors: "scan",
				reservation_errors: "reservation",
				checkin_errors: "checkin",
			};
			const type = typeMapping[name] || name;
			console.log('🔧 Type mapping:', { name, type });

			const particularStats = await getOverview({
				brand_id,
				name: type,
				range,
			});

			console.log('📊 API Response:', particularStats);
			console.log('📊 particularStats.current:', particularStats.current);
			console.log('📊 particularStats.previous:', particularStats.previous);

			const errorNames = [
				"scan_errors",
				"reservation_errors",
				"checkin_errors",
			];
			if (errorNames.includes(name)) {
				console.log('🔴 Processing error chart:', name);
				this[name] = calculateErrors(particularStats.current?.[type]) ?? [];
			} else {
				console.log('📈 Processing regular chart:', name);
				this[name] = particularStats.current?.[type] ?? [];
				this[`previous${name.charAt(0).toUpperCase() + name.slice(1)}`] =
					particularStats.previous?.[type] ?? [];
			}
			console.log('✅ Chart data set successfully for:', name);
		},

		async reset(): Promise<void> {
			this.search_funnel = null;
			this.guest_funnel = null;
			this.checkin = null;
			this.reservation = null;
			this.scan = null;
			this.previousCheckin = null;
			this.previousReservation = null;
			this.previousScan = null;
			this.reservation_errors = null;
			this.scan_errors = null;
			this.checkin_errors = null;
			this.checkinSource = null;
		},
	},
});
