<template>
  <uiModal
    modal-name="editUserModal"
    :title="t('editUserModal.title')"
    :actions="[
      { value: 'close', name: t('errorMessageModal.closeButton') },
      { value: 'edit', name: t('editUserModal.button') },
    ]"
    :open="isEditUserModalShown"
    type="default"
    @modalAction="handleModalAction"
  >
    <p class="text-sm text-gray-500 mb-4">
      {{ t("editUserModal.text") }}
    </p>
    <uiInput
      v-model="brandIds"
      :loading="false"
      class="mb-4"
      :label="t('userCreateView.brandsLabel')"
      name="brandIds"
      :placeholder="t('userCreateView.brandsPlaceholder')"
      :value="brandIds"
      @input-changed="handleBrandChange"
    />
    <uiSelect
      v-model="selectedRole"
      :loading="false"
      class="mb-4"
      :label="t('userCreateView.roleLabel')"
      :items="
        rolesToShow.map((role) => ({
          name: role,
          id: role,
        }))
      "
      :select="selectedRole"
      @selectChanged="handleSelect"
    />
    <uiToggle
      class="inputField"
      :item="{ title: t('userCreateView.mfaLabel'), active: false }"
      :checked="mfaRequired"
      :loading="false"
      @toggleChanged="(event) => (mfaRequired = event.active)"
    />
  </uiModal>
</template>

<script setup lang="ts">
import { t } from "@/locales";
import { ref, watch } from "vue";
import type { Ref } from "vue";
import { useUserListStore } from "@/stores/userList";
const userListStore = useUserListStore();

const isEditUserModalShown = ref(false);
const props = defineProps({
  items: {
    type: Array as () => number[],
    required: true,
  },
});
const emits = defineEmits(["editUsersModal", "brandsEdited", "rolesEdited"]);
const rolesToShow = ref(["admin", "brand_admin", "reception"]);
const selectedRole: Ref<Object> = ref({
  id: "",
  name: "",
});
const brandIds: Ref<string | null> = ref("");
const mfaRequired: Ref<boolean> = ref(false);

const handleModalAction = (action: any) => {
  emits("editUsersModal", {
    modal: "editUsersModal",
    action: action.action,
    selectedBrands: brandIds.value,
    selectedGroup: selectedRole.value,
    selectedMfaRequired: mfaRequired.value,
    usersIndex: props.items,
  });
};

const handleSelect = (event: { name: string; id: string }) => {
  selectedRole.value = event;
  emits("rolesEdited", event);
};

const handleBrandChange = (brandData: any) => {
  brandIds.value = brandData.value;
  emits("brandsEdited", brandData);
};

watch(
  () => props.items,
  (newValue) => {
    if (props.items.length === 1 && newValue) {
      const userIndexToEdit = props.items[0];
      const user = userListStore.filteredItems?.[userIndexToEdit];
      if (user) {
        brandIds.value = user.brand_ids;
        selectedRole.value = {
          id: user.group,
          name: user.group,
        };
        mfaRequired.value = user.mfa_required === "true";
      }
    }
  }
);
</script>
